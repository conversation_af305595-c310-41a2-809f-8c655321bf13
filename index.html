<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Visionary Cinematographer</title>
    <meta name="description"
        content="Award-winning cinematographer and visual artist <PERSON><PERSON><PERSON> creates extraordinary visual narratives that transcend reality through cutting-edge cinematography and photography.">
    <meta name="author" content="<PERSON><PERSON><PERSON>">
    <meta name="keywords"
        content="cinematography, photography, <PERSON><PERSON><PERSON>, portfolio, visual storytelling, creative direction, professional photographer, visual artist, cinematic excellence">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<PERSON><PERSON><PERSON> - Visionary Cinematographer">
    <meta property="og:description"
        content="Award-winning cinematographer creating extraordinary visual narratives that transcend reality.">
    <meta property="og:type" content="website">
    <meta property="og:image" content="assets/DSCF7010.jpeg">

    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-9T1GT9RXZY"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());
        gtag('config', 'G-9T1GT9RXZY');
    </script>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600&display=swap"
        rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="styles.css">

    <!-- Favicon -->
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
</head>

<body>
    <!-- Cursor -->
    <div class="cursor" id="cursor">
        <div class="cursor-dot"></div>
        <div class="cursor-ring"></div>
    </div>

    <!-- Background Particles -->
    <div class="particles-container" id="particles"></div>

    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-logo">
                <div class="logo-text">VIGNESH M NAIR</div>
                <div class="logo-subtitle">VISIONARY CINEMATOGRAPHER</div>
            </div>
            <div class="loading-progress-container">
                <div class="loading-progress" id="loadingProgress"></div>
                <div class="loading-percentage" id="loadingPercentage">0%</div>
            </div>
            <div class="loading-neural-network">
                <div class="neural-node"></div>
                <div class="neural-node"></div>
                <div class="neural-node"></div>
                <div class="neural-connection"></div>
                <div class="neural-connection"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <div class="logo-icon">
                    <div class="logo-hexagon"></div>
                    <span class="logo-text">VMN</span>
                </div>
            </div>
            <div class="nav-menu" id="navMenu">
                <a href="#home" class="nav-link active" data-text="HOME">
                    <span class="nav-text">HOME</span>
                    <span class="nav-number">01</span>
                </a>
                <a href="#portfolio" class="nav-link" data-text="PORTFOLIO">
                    <span class="nav-text">PORTFOLIO</span>
                    <span class="nav-number">02</span>
                </a>
                <a href="#about" class="nav-link" data-text="ABOUT">
                    <span class="nav-text">ABOUT</span>
                    <span class="nav-number">03</span>
                </a>
                <a href="#contact" class="nav-link" data-text="CONTACT">
                    <span class="nav-text">CONTACT</span>
                    <span class="nav-number">04</span>
                </a>
            </div>
            <div class="nav-toggle" id="navToggle">
                <span class="toggle-line"></span>
                <span class="toggle-line"></span>
                <span class="toggle-line"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="hero-image-container">
                <img src="assets/DSCF7010.jpeg" alt="Hero Background" class="hero-image">
                <div class="hero-overlay"></div>
                <div class="hero-grid"></div>
            </div>
            <div class="hero-particles"></div>
        </div>

        <div class="hero-content">
            <div class="hero-main">
                <div class="hero-badge">
                    <span class="badge-text">AWARD-WINNING CINEMATOGRAPHER</span>
                    <div class="badge-glow"></div>
                </div>

                <h1 class="hero-title">
                    <div class="title-line">
                        <span class="title-word" data-text="VIGNESH">VIGNESH</span>
                        <span class="title-accent">M</span>
                        <span class="title-word" data-text="NAIR">NAIR</span>
                    </div>
                    <div class="title-subtitle">
                        <span class="subtitle-text">VISIONARY</span>
                        <span class="subtitle-divider"></span>
                        <span class="subtitle-text">CINEMATOGRAPHER</span>
                    </div>
                </h1>

                <div class="hero-description">
                    <p class="description-text">
                        Creating extraordinary visual narratives that transcend reality through
                        <span class="highlight">cutting-edge cinematography</span> and
                        <span class="highlight">artistic vision</span>
                    </p>
                </div>

                <div class="hero-cta">
                    <button class="cta-primary" id="viewWork">
                        <span class="cta-text">VIEW MY WORK</span>
                        <div class="cta-arrow">
                            <svg viewBox="0 0 24 24" fill="none">
                                <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" stroke-width="2" />
                            </svg>
                        </div>
                        <div class="cta-glow"></div>
                    </button>
                    <button class="cta-secondary" id="contactMe">
                        <span class="cta-text">GET IN TOUCH</span>
                        <div class="cta-border"></div>
                    </button>
                </div>
            </div>

            <div class="hero-stats">
                <div class="stat-item">
                    <div class="stat-number" data-count="150">0</div>
                    <div class="stat-label">Projects Completed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" data-count="8">0</div>
                    <div class="stat-label">Years Experience</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" data-count="25">0</div>
                    <div class="stat-label">Awards Won</div>
                </div>
            </div>
        </div>

        <div class="hero-scroll">
            <div class="scroll-indicator">
                <span class="scroll-text">EXPLORE</span>
                <div class="scroll-line"></div>
                <div class="scroll-mouse">
                    <div class="scroll-wheel"></div>
                </div>
            </div>
        </div>

        <div class="hero-social">
            <div class="social-label">FOLLOW</div>
            <a href="https://www.instagram.com/vigneshmnair/" target="_blank" class="social-link">
                <span class="social-text">INSTAGRAM</span>
                <div class="social-icon">IG</div>
            </a>
            <a href="https://www.flickr.com/people/vigneshmnair/" target="_blank" class="social-link">
                <span class="social-text">BEHANCE</span>
                <div class="social-icon">BE</div>
            </a>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="portfolio">
        <div class="portfolio-background">
            <div class="portfolio-grid-bg"></div>
        </div>

        <div class="container">
            <div class="section-header">
                <div class="section-badge">
                    <span class="badge-number">02</span>
                    <span class="badge-text">PORTFOLIO</span>
                </div>
                <h2 class="section-title">
                    <span class="title-main">SELECTED</span>
                    <span class="title-accent">WORKS</span>
                </h2>
                <p class="section-subtitle">
                    A curated collection of extraordinary visual narratives that push the boundaries of
                    <span class="highlight">cinematography</span> and <span class="highlight">artistic vision</span>
                </p>
            </div>

            <!-- Portfolio Filter -->
            <div class="portfolio-filter">
                <button class="filter-btn active" data-filter="all">
                    <span class="filter-text">ALL WORKS</span>
                    <span class="filter-count">06</span>
                </button>
                <button class="filter-btn" data-filter="cinematic">
                    <span class="filter-text">CINEMATIC</span>
                    <span class="filter-count">03</span>
                </button>
                <button class="filter-btn" data-filter="portrait">
                    <span class="filter-text">PORTRAIT</span>
                    <span class="filter-count">02</span>
                </button>
                <button class="filter-btn" data-filter="landscape">
                    <span class="filter-text">LANDSCAPE</span>
                    <span class="filter-count">01</span>
                </button>
            </div>

            <!-- 3D Portfolio Grid -->
            <div class="portfolio-grid" id="portfolioGrid">
                <div class="portfolio-item" data-category="cinematic" data-index="1">
                    <div class="item-container">
                        <div class="item-image">
                            <img src="assets/DSCF7010.jpeg" alt="Cinematic Landscape" loading="lazy">
                            <div class="item-overlay">
                                <div class="overlay-content">
                                    <div class="item-category">CINEMATIC</div>
                                    <h3 class="item-title">ETHEREAL LANDSCAPES</h3>
                                    <p class="item-description">Capturing the sublime beauty of nature through cinematic
                                        storytelling</p>
                                    <div class="item-tech">
                                        <span class="tech-tag">4K CINEMA</span>
                                        <span class="tech-tag">COLOR GRADING</span>
                                    </div>
                                </div>
                                <div class="overlay-cta">
                                    <button class="view-btn">
                                        <span>VIEW PROJECT</span>
                                        <svg viewBox="0 0 24 24" fill="none">
                                            <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor"
                                                stroke-width="2" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="item-glow"></div>
                    </div>
                </div>

                <div class="portfolio-item" data-category="portrait" data-index="2">
                    <div class="item-container">
                        <div class="item-image">
                            <img src="assets/DSCF7639E_2.jpg" alt="Portrait Study" loading="lazy">
                            <div class="item-overlay">
                                <div class="overlay-content">
                                    <div class="item-category">PORTRAIT</div>
                                    <h3 class="item-title">HUMAN ESSENCE</h3>
                                    <p class="item-description">Revealing the depth of human emotion through intimate
                                        portraiture</p>
                                    <div class="item-tech">
                                        <span class="tech-tag">STUDIO LIGHTING</span>
                                        <span class="tech-tag">RETOUCHING</span>
                                    </div>
                                </div>
                                <div class="overlay-cta">
                                    <button class="view-btn">
                                        <span>VIEW PROJECT</span>
                                        <svg viewBox="0 0 24 24" fill="none">
                                            <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor"
                                                stroke-width="2" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="item-glow"></div>
                    </div>
                </div>

                <div class="portfolio-item" data-category="cinematic" data-index="3">
                    <div class="item-container">
                        <div class="item-image">
                            <img src="assets/DSCF7027_cut.jpeg" alt="Creative Vision" loading="lazy">
                            <div class="item-overlay">
                                <div class="overlay-content">
                                    <div class="item-category">CINEMATIC</div>
                                    <h3 class="item-title">ARTISTIC VISION</h3>
                                    <p class="item-description">Pushing creative boundaries through experimental
                                        cinematography</p>
                                    <div class="item-tech">
                                        <span class="tech-tag">EXPERIMENTAL</span>
                                        <span class="tech-tag">VISUAL FX</span>
                                    </div>
                                </div>
                                <div class="overlay-cta">
                                    <button class="view-btn">
                                        <span>VIEW PROJECT</span>
                                        <svg viewBox="0 0 24 24" fill="none">
                                            <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor"
                                                stroke-width="2" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="item-glow"></div>
                    </div>
                </div>

                <div class="portfolio-item" data-category="landscape" data-index="4">
                    <div class="item-container">
                        <div class="item-image">
                            <img src="assets/jp1.jpg" alt="Landscape Photography" loading="lazy">
                            <div class="item-overlay">
                                <div class="overlay-content">
                                    <div class="item-category">LANDSCAPE</div>
                                    <h3 class="item-title">NATURAL GRANDEUR</h3>
                                    <p class="item-description">Documenting the majestic beauty of untouched landscapes
                                    </p>
                                    <div class="item-tech">
                                        <span class="tech-tag">DRONE CINEMA</span>
                                        <span class="tech-tag">TIME-LAPSE</span>
                                    </div>
                                </div>
                                <div class="overlay-cta">
                                    <button class="view-btn">
                                        <span>VIEW PROJECT</span>
                                        <svg viewBox="0 0 24 24" fill="none">
                                            <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor"
                                                stroke-width="2" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="item-glow"></div>
                    </div>
                </div>

                <div class="portfolio-item" data-category="cinematic" data-index="5">
                    <div class="item-container">
                        <div class="item-image">
                            <img src="assets/LRM_EXPORT_20210702_121733.jpg" alt="Documentary Style" loading="lazy">
                            <div class="item-overlay">
                                <div class="overlay-content">
                                    <div class="item-category">DOCUMENTARY</div>
                                    <h3 class="item-title">STORIES IN MOTION</h3>
                                    <p class="item-description">Capturing authentic moments that tell powerful human
                                        stories</p>
                                    <div class="item-tech">
                                        <span class="tech-tag">DOCUMENTARY</span>
                                        <span class="tech-tag">STORYTELLING</span>
                                    </div>
                                </div>
                                <div class="overlay-cta">
                                    <button class="view-btn">
                                        <span>VIEW PROJECT</span>
                                        <svg viewBox="0 0 24 24" fill="none">
                                            <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor"
                                                stroke-width="2" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="item-glow"></div>
                    </div>
                </div>

                <div class="portfolio-item" data-category="portrait" data-index="6">
                    <div class="item-container">
                        <div class="item-image">
                            <img src="assets/DSCF7026.jpeg" alt="Cinematic Frame" loading="lazy">
                            <div class="item-overlay">
                                <div class="overlay-content">
                                    <div class="item-category">PORTRAIT</div>
                                    <h3 class="item-title">CINEMATIC PORTRAITS</h3>
                                    <p class="item-description">Blending portrait photography with cinematic
                                        storytelling techniques</p>
                                    <div class="item-tech">
                                        <span class="tech-tag">CINEMATIC</span>
                                        <span class="tech-tag">LIGHTING</span>
                                    </div>
                                </div>
                                <div class="overlay-cta">
                                    <button class="view-btn">
                                        <span>VIEW PROJECT</span>
                                        <svg viewBox="0 0 24 24" fill="none">
                                            <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor"
                                                stroke-width="2" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="item-glow"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="about-background">
            <div class="about-grid"></div>
            <div class="about-particles"></div>
        </div>

        <div class="container">
            <div class="section-header">
                <div class="section-badge">
                    <span class="badge-number">03</span>
                    <span class="badge-text">ABOUT</span>
                </div>
                <h2 class="section-title">
                    <span class="title-main">VISIONARY</span>
                    <span class="title-accent">ARTIST</span>
                </h2>
            </div>

            <div class="about-content">
                <div class="about-main">
                    <div class="about-text">
                        <div class="text-block">
                            <h3 class="text-title">CREATIVE PHILOSOPHY</h3>
                            <p class="text-description">
                                I believe in the power of visual storytelling to transcend boundaries and connect souls.
                                Every frame I capture is a window into a world where <span class="highlight">emotion
                                    meets artistry</span>,
                                where technical excellence serves the greater purpose of human connection.
                            </p>
                        </div>

                        <div class="text-block">
                            <h3 class="text-title">TECHNICAL MASTERY</h3>
                            <p class="text-description">
                                With cutting-edge equipment and years of experience, I push the boundaries of what's
                                possible
                                in cinematography. From <span class="highlight">8K cinema cameras</span> to advanced
                                color grading,
                                every technical choice serves the story.
                            </p>
                        </div>

                        <div class="about-skills">
                            <div class="skill-category">
                                <h4 class="skill-title">CINEMATOGRAPHY</h4>
                                <div class="skill-tags">
                                    <span class="skill-tag">RED CINEMA</span>
                                    <span class="skill-tag">ARRI ALEXA</span>
                                    <span class="skill-tag">BLACKMAGIC</span>
                                    <span class="skill-tag">DRONE CINEMA</span>
                                </div>
                            </div>
                            <div class="skill-category">
                                <h4 class="skill-title">POST-PRODUCTION</h4>
                                <div class="skill-tags">
                                    <span class="skill-tag">DAVINCI RESOLVE</span>
                                    <span class="skill-tag">PREMIERE PRO</span>
                                    <span class="skill-tag">AFTER EFFECTS</span>
                                    <span class="skill-tag">COLOR GRADING</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="about-visual">
                        <div class="visual-container">
                            <div class="visual-image">
                                <img src="assets/DSCF7639E.jpg" alt="Vignesh M Nair" loading="lazy">
                                <div class="image-overlay">
                                    <div class="overlay-pattern"></div>
                                </div>
                            </div>
                            <div class="visual-frame"></div>
                            <div class="visual-glow"></div>
                        </div>

                        <div class="about-achievements">
                            <div class="achievement-item">
                                <div class="achievement-icon">🏆</div>
                                <div class="achievement-text">
                                    <span class="achievement-title">AWARD WINNER</span>
                                    <span class="achievement-desc">International Film Festival</span>
                                </div>
                            </div>
                            <div class="achievement-item">
                                <div class="achievement-icon">🎬</div>
                                <div class="achievement-text">
                                    <span class="achievement-title">FEATURED ARTIST</span>
                                    <span class="achievement-desc">Cinema Magazine</span>
                                </div>
                            </div>
                            <div class="achievement-item">
                                <div class="achievement-icon">⭐</div>
                                <div class="achievement-text">
                                    <span class="achievement-title">CLIENT SATISFACTION</span>
                                    <span class="achievement-desc">100% Rating</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="about-metrics">
                    <div class="metric-item">
                        <div class="metric-number" data-count="8">0</div>
                        <div class="metric-label">YEARS OF EXCELLENCE</div>
                        <div class="metric-bar">
                            <div class="metric-fill" style="width: 80%"></div>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-number" data-count="150">0</div>
                        <div class="metric-label">PROJECTS DELIVERED</div>
                        <div class="metric-bar">
                            <div class="metric-fill" style="width: 95%"></div>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-number" data-count="25">0</div>
                        <div class="metric-label">AWARDS RECEIVED</div>
                        <div class="metric-bar">
                            <div class="metric-fill" style="width: 90%"></div>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-number" data-count="100">0</div>
                        <div class="metric-label">CLIENT SATISFACTION</div>
                        <div class="metric-bar">
                            <div class="metric-fill" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="contact-background">
            <div class="contact-grid"></div>
            <div class="contact-particles"></div>
        </div>

        <div class="container">
            <div class="section-header">
                <div class="section-badge">
                    <span class="badge-number">04</span>
                    <span class="badge-text">CONTACT</span>
                </div>
                <h2 class="section-title">
                    <span class="title-main">LET'S CREATE</span>
                    <span class="title-accent">TOGETHER</span>
                </h2>
                <p class="section-subtitle">
                    Ready to bring your vision to life? Let's collaborate on something extraordinary that will
                    <span class="highlight">captivate audiences</span> and <span class="highlight">tell your
                        story</span>
                </p>
            </div>

            <div class="contact-content">
                <div class="contact-info">
                    <div class="info-card">
                        <div class="card-icon">
                            <svg viewBox="0 0 24 24" fill="none">
                                <path
                                    d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z"
                                    stroke="currentColor" stroke-width="2" />
                                <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2" />
                            </svg>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">EMAIL</h3>
                            <a href="mailto:<EMAIL>" class="card-link"><EMAIL></a>
                            <p class="card-desc">For project inquiries and collaborations</p>
                        </div>
                        <div class="card-glow"></div>
                    </div>

                    <div class="info-card">
                        <div class="card-icon">
                            <svg viewBox="0 0 24 24" fill="none">
                                <path
                                    d="M22 16.92V19.92C22 20.52 21.52 21 20.92 21C9.4 21 0 11.6 0 0.08C0 -0.52 0.48 -1 1.08 -1H4.08C4.68 -1 5.16 -0.52 5.16 0.08V3.08"
                                    stroke="currentColor" stroke-width="2" />
                            </svg>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">PHONE</h3>
                            <a href="tel:+1234567890" class="card-link">+****************</a>
                            <p class="card-desc">Available for urgent project discussions</p>
                        </div>
                        <div class="card-glow"></div>
                    </div>

                    <div class="info-card">
                        <div class="card-icon">
                            <svg viewBox="0 0 24 24" fill="none">
                                <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 5.03 7.03 1 12 1S21 5.03 21 10Z"
                                    stroke="currentColor" stroke-width="2" />
                                <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2" />
                            </svg>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">LOCATION</h3>
                            <span class="card-link">Global Remote</span>
                            <p class="card-desc">Available worldwide for projects</p>
                        </div>
                        <div class="card-glow"></div>
                    </div>

                    <div class="social-connect">
                        <h3 class="social-title">CONNECT WITH ME</h3>
                        <div class="social-links">
                            <a href="https://www.instagram.com/vigneshmnair/" target="_blank" class="social-link">
                                <div class="social-icon">IG</div>
                                <span class="social-text">Instagram</span>
                            </a>
                            <a href="https://www.flickr.com/people/vigneshmnair/" target="_blank" class="social-link">
                                <div class="social-icon">BE</div>
                                <span class="social-text">Behance</span>
                            </a>
                            <a href="#" target="_blank" class="social-link">
                                <div class="social-icon">LI</div>
                                <span class="social-text">LinkedIn</span>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="contact-form-container">
                    <form class="contact-form" id="contactForm">
                        <div class="form-header">
                            <h3 class="form-title">START A PROJECT</h3>
                            <p class="form-subtitle">Tell me about your vision</p>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">NAME</label>
                                <input type="text" name="name" class="form-input" placeholder="Your full name" required>
                                <div class="input-line"></div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">EMAIL</label>
                                <input type="email" name="email" class="form-input" placeholder="<EMAIL>"
                                    required>
                                <div class="input-line"></div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">PROJECT TYPE</label>
                                <select name="project-type" class="form-select" required>
                                    <option value="">Select project type</option>
                                    <option value="commercial">Commercial Film</option>
                                    <option value="documentary">Documentary</option>
                                    <option value="portrait">Portrait Session</option>
                                    <option value="event">Event Coverage</option>
                                    <option value="other">Other</option>
                                </select>
                                <div class="input-line"></div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">BUDGET RANGE</label>
                                <select name="budget" class="form-select" required>
                                    <option value="">Select budget range</option>
                                    <option value="5k-10k">$5K - $10K</option>
                                    <option value="10k-25k">$10K - $25K</option>
                                    <option value="25k-50k">$25K - $50K</option>
                                    <option value="50k+">$50K+</option>
                                </select>
                                <div class="input-line"></div>
                            </div>
                        </div>

                        <div class="form-group full-width">
                            <label class="form-label">PROJECT DETAILS</label>
                            <textarea name="message" class="form-textarea"
                                placeholder="Tell me about your project, timeline, and vision..." rows="6"
                                required></textarea>
                            <div class="input-line"></div>
                        </div>

                        <button type="submit" class="form-submit">
                            <span class="submit-text">SEND PROJECT BRIEF</span>
                            <div class="submit-arrow">
                                <svg viewBox="0 0 24 24" fill="none">
                                    <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" stroke-width="2" />
                                </svg>
                            </div>
                            <div class="submit-glow"></div>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-background">
            <div class="footer-grid"></div>
        </div>
        <div class="container">
            <div class="footer-content">
                <div class="footer-main">
                    <div class="footer-logo">
                        <div class="logo-icon">
                            <div class="logo-hexagon"></div>
                            <span class="logo-text">VMN</span>
                        </div>
                        <p class="footer-tagline">Visionary Cinematographer</p>
                    </div>

                    <div class="footer-links">
                        <div class="link-group">
                            <h4 class="link-title">NAVIGATION</h4>
                            <a href="#home" class="footer-link">Home</a>
                            <a href="#portfolio" class="footer-link">Portfolio</a>
                            <a href="#about" class="footer-link">About</a>
                            <a href="#contact" class="footer-link">Contact</a>
                        </div>

                        <div class="link-group">
                            <h4 class="link-title">SERVICES</h4>
                            <a href="#" class="footer-link">Commercial Films</a>
                            <a href="#" class="footer-link">Documentaries</a>
                            <a href="#" class="footer-link">Portrait Sessions</a>
                            <a href="#" class="footer-link">Event Coverage</a>
                        </div>

                        <div class="link-group">
                            <h4 class="link-title">CONNECT</h4>
                            <a href="https://www.instagram.com/vigneshmnair/" class="footer-link">Instagram</a>
                            <a href="https://www.flickr.com/people/vigneshmnair/" class="footer-link">Behance</a>
                            <a href="#" class="footer-link">LinkedIn</a>
                            <a href="mailto:<EMAIL>" class="footer-link">Email</a>
                        </div>
                    </div>
                </div>

                <div class="footer-bottom">
                    <div class="footer-copyright">
                        <p>&copy; 2024 Vignesh M Nair. All rights reserved.</p>
                    </div>
                    <div class="footer-back-to-top">
                        <button class="back-to-top" id="backToTop">
                            <span>BACK TO TOP</span>
                            <svg viewBox="0 0 24 24" fill="none">
                                <path d="M7 14L12 9L17 14" stroke="currentColor" stroke-width="2" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Advanced Lightbox -->
    <div class="lightbox" id="lightbox">
        <div class="lightbox-overlay"></div>
        <div class="lightbox-container">
            <div class="lightbox-content">
                <button class="lightbox-close" id="lightboxClose">
                    <svg viewBox="0 0 24 24" fill="none">
                        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" />
                    </svg>
                </button>
                <div class="lightbox-image-container">
                    <img src="" alt="" id="lightboxImage">
                </div>
                <div class="lightbox-info">
                    <div class="lightbox-category" id="lightboxCategory"></div>
                    <h3 class="lightbox-title" id="lightboxTitle"></h3>
                    <p class="lightbox-description" id="lightboxDescription"></p>
                    <div class="lightbox-tech" id="lightboxTech"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
</body>

</html>