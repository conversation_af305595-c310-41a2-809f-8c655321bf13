/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #0a0a0a;
    --secondary-color: #ffffff;
    --accent-color: #f8f9fa;
    --accent-dark: #f1f3f4;
    --text-primary: #1a1a1a;
    --text-secondary: #4a5568;
    --text-light: #718096;
    --text-muted: #a0aec0;
    --border-color: #e2e8f0;
    --border-light: #f7fafc;
    --shadow-light: 0 4px 25px rgba(0, 0, 0, 0.08);
    --shadow-medium: 0 10px 50px rgba(0, 0, 0, 0.12);
    --shadow-heavy: 0 20px 80px rgba(0, 0, 0, 0.15);
    --shadow-elegant: 0 8px 32px rgba(0, 0, 0, 0.1);
    --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-fast: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-elegant: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-display: 'Playfair Display', serif;
    --gradient-elegant: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 50%, rgba(0, 0, 0, 0.4) 100%);
    --gradient-overlay: linear-gradient(135deg, rgba(0, 0, 0, 0.75) 0%, rgba(0, 0, 0, 0.5) 100%);
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--secondary-color);
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.8s cubic-bezier(0.23, 1, 0.32, 1), visibility 0.8s cubic-bezier(0.23, 1, 0.32, 1);
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    transform: translateY(-20px);
    animation: fadeInUp 1s ease-out;
}

.loading-text {
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: 400;
    letter-spacing: 0.15em;
    margin-bottom: 3rem;
    color: var(--text-primary);
    opacity: 0.9;
}

.loading-bar {
    width: 250px;
    height: 1px;
    background: var(--border-color);
    position: relative;
    overflow: hidden;
    border-radius: 2px;
}

.loading-progress {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--text-secondary) 100%);
    animation: loading 2.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }

    50% {
        left: 0%;
    }

    100% {
        left: 100%;
    }
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.92);
    backdrop-filter: blur(25px) saturate(180%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    z-index: 1000;
    transition: var(--transition-elegant);
    transform: translateY(0);
    box-shadow: 0 1px 20px rgba(0, 0, 0, 0.04);
}

.navbar.hidden {
    transform: translateY(-100%);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.nav-logo a {
    font-family: var(--font-display);
    font-size: 1.75rem;
    font-weight: 500;
    color: var(--text-primary);
    text-decoration: none;
    letter-spacing: 0.05em;
    transition: var(--transition-fast);
}

.nav-logo a:hover {
    color: var(--text-secondary);
    transform: translateY(-1px);
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-primary);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--text-primary);
    transition: var(--transition-fast);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 2px;
    background: var(--text-primary);
    transition: var(--transition-fast);
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-elegant);
    z-index: -1;
}

.hero-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
    z-index: 1;
}

.hero-content {
    text-align: center;
    color: var(--secondary-color);
    z-index: 2;
    position: relative;
    animation: fadeInUp 1.2s ease-out 0.5s both;
}

.hero-title {
    margin-bottom: 2rem;
}

.hero-name {
    display: block;
    font-family: var(--font-display);
    font-size: clamp(3.5rem, 8vw, 7rem);
    font-weight: 400;
    line-height: 1.05;
    letter-spacing: -0.02em;
    margin-bottom: 1rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    display: block;
    font-family: var(--font-primary);
    font-size: clamp(1.1rem, 2.5vw, 1.75rem);
    font-weight: 300;
    letter-spacing: 0.25em;
    text-transform: uppercase;
    opacity: 0.95;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
}

.hero-description {
    font-size: 1.25rem;
    font-weight: 300;
    max-width: 650px;
    margin: 0 auto 4rem;
    opacity: 0.92;
    line-height: 1.7;
    text-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);
    letter-spacing: 0.01em;
}

.hero-scroll {
    position: absolute;
    bottom: 3rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 2;
    animation: fadeInUp 1.5s ease-out 1s both;
}

.scroll-indicator span {
    display: block;
    font-size: 0.85rem;
    letter-spacing: 0.15em;
    text-transform: uppercase;
    margin-bottom: 1.5rem;
    opacity: 0.85;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.scroll-line {
    width: 1px;
    height: 50px;
    background: var(--secondary-color);
    margin: 0 auto;
    opacity: 0.7;
    animation: scroll-bounce 3s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

@keyframes scroll-bounce {

    0%,
    100% {
        transform: translateY(0);
        opacity: 0.6;
    }

    50% {
        transform: translateY(10px);
        opacity: 1;
    }
}

.hero-social {
    position: absolute;
    bottom: 3rem;
    right: 3rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    z-index: 2;
    animation: fadeInRight 1.5s ease-out 1.2s both;
}

.social-link {
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 0.85rem;
    letter-spacing: 0.12em;
    text-transform: uppercase;
    opacity: 0.8;
    transition: var(--transition-elegant);
    writing-mode: vertical-rl;
    text-orientation: mixed;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    position: relative;
}

.social-link::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 0;
    background: var(--secondary-color);
    transition: var(--transition-elegant);
}

.social-link:hover {
    opacity: 1;
    transform: translateY(-3px) translateX(5px);
}

.social-link:hover::before {
    height: 20px;
}

/* ===== SECTION HEADERS ===== */
.section-header {
    text-align: center;
    margin-bottom: 5rem;
    position: relative;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: -2rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--text-primary) 0%, var(--text-secondary) 100%);
    border-radius: 2px;
}

.section-title {
    font-family: var(--font-display);
    font-size: clamp(2.75rem, 5vw, 4.5rem);
    font-weight: 400;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    line-height: 1.15;
    letter-spacing: -0.02em;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 650px;
    margin: 0 auto;
    line-height: 1.7;
    font-weight: 300;
}

/* ===== PORTFOLIO SECTION ===== */
.portfolio {
    padding: 10rem 0;
    background: var(--secondary-color);
    position: relative;
}

.portfolio::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--border-color) 50%, transparent 100%);
}

.bento-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-auto-rows: 300px;
    gap: 1.5rem;
    margin-top: 2rem;
}

.bento-item {
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    cursor: pointer;
    transition: var(--transition-elegant);
    box-shadow: var(--shadow-light);
}

.bento-item:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: var(--shadow-heavy);
}

.bento-large {
    grid-column: span 8;
    grid-row: span 2;
}

.bento-medium {
    grid-column: span 4;
    grid-row: span 2;
}

.bento-small {
    grid-column: span 4;
    grid-row: span 1;
}

.bento-wide {
    grid-column: span 8;
    grid-row: span 1;
}

.bento-tall {
    grid-column: span 4;
    grid-row: span 2;
}

.bento-square {
    grid-column: span 4;
    grid-row: span 1;
}

.bento-image {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.bento-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-smooth);
}

.bento-item:hover .bento-image img {
    transform: scale(1.05);
}

.bento-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-overlay);
    display: flex;
    align-items: flex-end;
    padding: 2.5rem;
    opacity: 0;
    transition: var(--transition-elegant);
    backdrop-filter: blur(2px);
}

.bento-item:hover .bento-overlay {
    opacity: 1;
}

.bento-content h3 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.bento-content p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    line-height: 1.6;
}

/* ===== ABOUT SECTION ===== */
.about {
    padding: 10rem 0;
    background: var(--accent-color);
    position: relative;
}

.about::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--border-color) 50%, transparent 100%);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text {
    padding-right: 2rem;
}

.about-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 3rem;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.about-image {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-elegant);
    transition: var(--transition-elegant);
}

.about-image:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
}

.about-image img {
    width: 100%;
    height: 550px;
    object-fit: cover;
    transition: var(--transition-elegant);
}

.about-image:hover img {
    transform: scale(1.05);
}

/* ===== CONTACT SECTION ===== */
.contact {
    padding: 10rem 0;
    background: var(--secondary-color);
    position: relative;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--border-color) 50%, transparent 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 2rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.contact-item h3 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.contact-item a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 1.1rem;
    transition: var(--transition-fast);
}

.contact-item a:hover {
    color: var(--text-primary);
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    position: relative;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1.25rem 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-family: var(--font-primary);
    font-size: 1rem;
    background: var(--secondary-color);
    transition: var(--transition-elegant);
    resize: vertical;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.02);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--text-primary);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.btn-submit {
    padding: 1.25rem 3rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--text-primary) 100%);
    color: var(--secondary-color);
    border: none;
    border-radius: 12px;
    font-family: var(--font-primary);
    font-size: 1rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    cursor: pointer;
    transition: var(--transition-elegant);
    box-shadow: var(--shadow-elegant);
    position: relative;
    overflow: hidden;
}

.btn-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-fast);
}

.btn-submit:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

.btn-submit:hover::before {
    left: 100%;
}

/* ===== FOOTER ===== */
.footer {
    padding: 3rem 0;
    background: var(--primary-color);
    color: var(--secondary-color);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-left p {
    font-size: 0.9rem;
    opacity: 0.8;
}

.footer-right a {
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transition: var(--transition-fast);
}

.footer-right a:hover {
    opacity: 0.8;
}

/* ===== LIGHTBOX ===== */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-smooth);
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    text-align: center;
}

.lightbox-close {
    position: absolute;
    top: -50px;
    right: 0;
    color: var(--secondary-color);
    font-size: 2rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.lightbox-close:hover {
    opacity: 0.7;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 8px;
}

.lightbox-caption {
    color: var(--secondary-color);
    margin-top: 1rem;
    font-size: 1rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .container {
        padding: 0 1.5rem;
    }

    .bento-large {
        grid-column: span 12;
        grid-row: span 1;
    }

    .bento-medium,
    .bento-wide {
        grid-column: span 6;
        grid-row: span 1;
    }

    .bento-small,
    .bento-tall,
    .bento-square {
        grid-column: span 6;
        grid-row: span 1;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        background: var(--secondary-color);
        flex-direction: column;
        padding: 2rem;
        box-shadow: var(--shadow-light);
        transform: translateY(-100%);
        transition: var(--transition-smooth);
        opacity: 0;
        visibility: hidden;
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-social {
        position: static;
        flex-direction: row;
        justify-content: center;
        margin-top: 2rem;
    }

    .social-link {
        writing-mode: initial;
        text-orientation: initial;
    }

    .bento-grid {
        grid-template-columns: 1fr;
        grid-auto-rows: 250px;
        gap: 1rem;
    }

    .bento-item {
        grid-column: span 1 !important;
        grid-row: span 1 !important;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .about-text {
        padding-right: 0;
    }

    .about-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .hero-scroll {
        bottom: 2rem;
    }

    .hero-social {
        bottom: 2rem;
        right: 2rem;
    }

    .section-header {
        margin-bottom: 3rem;
    }

    .portfolio,
    .about,
    .contact {
        padding: 4rem 0;
    }

    .about-stats {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .bento-grid {
        grid-auto-rows: 200px;
    }

    .bento-overlay {
        padding: 1rem;
    }

    .bento-content h3 {
        font-size: 1.2rem;
    }

    .lightbox-content {
        max-width: 95vw;
        max-height: 95vh;
    }

    .lightbox-close {
        top: -40px;
        font-size: 1.5rem;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: var(--transition-smooth);
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* ===== UTILITY CLASSES ===== */
.text-center {
    text-align: center;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-1 {
    margin-bottom: 1rem;
}

.mb-2 {
    margin-bottom: 2rem;
}

.mb-3 {
    margin-bottom: 3rem;
}

.mt-0 {
    margin-top: 0;
}

.mt-1 {
    margin-top: 1rem;
}

.mt-2 {
    margin-top: 2rem;
}

.mt-3 {
    margin-top: 3rem;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.bento-image img,
.about-image img,
.hero-image {
    will-change: transform;
}

.bento-item,
.nav-link,
.social-link,
.btn-submit {
    will-change: transform;
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .scroll-line {
        animation: none;
    }

    .loading-progress {
        animation: none;
    }
}

/* ===== FOCUS STYLES ===== */
.nav-link:focus,
.social-link:focus,
.btn-submit:focus,
.form-group input:focus,
.form-group textarea:focus {
    outline: 2px solid var(--text-primary);
    outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {

    .navbar,
    .hero-social,
    .hero-scroll,
    .contact-form,
    .footer {
        display: none;
    }

    .hero {
        height: auto;
        padding: 2rem 0;
    }

    .portfolio,
    .about,
    .contact {
        padding: 2rem 0;
    }
}