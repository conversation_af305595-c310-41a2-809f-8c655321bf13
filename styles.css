/* ===== FUTURISTIC PORTFOLIO - REVOLUTIONARY DESIGN ===== */

/* CSS RESET & BASE STYLES */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

:root {
    /* Core Colors - Dark Futuristic Theme */
    --bg-primary: #0a0a0f;
    --bg-secondary: #111118;
    --bg-tertiary: #1a1a24;
    --bg-glass: rgba(255, 255, 255, 0.05);
    --bg-glass-hover: rgba(255, 255, 255, 0.1);

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b8bcc8;
    --text-muted: #6b7280;
    --text-accent: #00d4ff;

    /* Accent Colors - Neon Cyberpunk */
    --accent-primary: #00d4ff;
    --accent-secondary: #ff0080;
    --accent-tertiary: #00ff88;
    --accent-warning: #ffaa00;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #00d4ff 0%, #0099cc 50%, #006699 100%);
    --gradient-secondary: linear-gradient(135deg, #ff0080 0%, #cc0066 50%, #990033 100%);
    --gradient-accent: linear-gradient(135deg, #00ff88 0%, #00cc66 50%, #009944 100%);
    --gradient-hero: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(255, 0, 128, 0.1) 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

    /* Shadows & Glows */
    --shadow-neon: 0 0 20px rgba(0, 212, 255, 0.3);
    --shadow-neon-hover: 0 0 40px rgba(0, 212, 255, 0.5);
    --shadow-pink: 0 0 20px rgba(255, 0, 128, 0.3);
    --shadow-green: 0 0 20px rgba(0, 255, 136, 0.3);
    --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3);
    --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.4);

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-display: 'Space Grotesk', sans-serif;
    --font-mono: 'JetBrains Mono', monospace;

    /* Transitions */
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Spacing */
    --space-xs: 0.5rem;
    --space-sm: 1rem;
    --space-md: 1.5rem;
    --space-lg: 2rem;
    --space-xl: 3rem;
    --space-2xl: 4rem;
    --space-3xl: 6rem;

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --radius-full: 50%;

    /* Z-Index */
    --z-background: -1;
    --z-content: 1;
    --z-overlay: 10;
    --z-modal: 100;
    --z-tooltip: 1000;
    --z-cursor: 9999;
}

/* Base Styles */
html {
    scroll-behavior: smooth;
    font-size: 16px;
    overflow-x: hidden;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
    position: relative;
    min-height: 100vh;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 255, 136, 0.05) 0%, transparent 50%);
    z-index: var(--z-background);
    pointer-events: none;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
    position: relative;
    z-index: var(--z-content);
}

/* Custom Cursor */
.cursor {
    position: fixed;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: var(--z-cursor);
    mix-blend-mode: difference;
}

.cursor-dot {
    width: 8px;
    height: 8px;
    background: var(--accent-primary);
    border-radius: var(--radius-full);
    transform: translate(-50%, -50%);
    transition: var(--transition-fast);
}

.cursor-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 32px;
    height: 32px;
    border: 2px solid var(--accent-primary);
    border-radius: var(--radius-full);
    transform: translate(-50%, -50%);
    transition: var(--transition-smooth);
    opacity: 0.5;
}

/* Particles Background */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: var(--z-background);
    pointer-events: none;
}

/* Glassmorphism Utility */
.glass {
    background: var(--bg-glass);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-hover:hover {
    background: var(--bg-glass-hover);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Neon Glow Effects */
.neon-blue {
    box-shadow: var(--shadow-neon);
}

.neon-blue:hover {
    box-shadow: var(--shadow-neon-hover);
}

.neon-pink {
    box-shadow: var(--shadow-pink);
}

.neon-green {
    box-shadow: var(--shadow-green);
}

/* Typography */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-glow {
    text-shadow: 0 0 20px currentColor;
}

/* Highlight Text */
.highlight {
    position: relative;
    color: var(--accent-primary);
    font-weight: 600;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transform-origin: left;
    transition: var(--transition-smooth);
}

.highlight:hover::after {
    transform: scaleX(1);
}

/* Section Badges */
.section-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-md);
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-xl);
    backdrop-filter: blur(20px);
}

.badge-number {
    font-family: var(--font-mono);
    font-size: 0.8rem;
    color: var(--accent-primary);
    font-weight: 600;
}

.badge-text {
    font-family: var(--font-mono);
    font-size: 0.8rem;
    color: var(--text-secondary);
    letter-spacing: 0.1em;
    text-transform: uppercase;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--space-3xl);
}

.section-title {
    font-family: var(--font-display);
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--space-lg);
}

.title-main {
    color: var(--text-primary);
}

.title-accent {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.7;
}

/* ===== FUTURISTIC LOADING SCREEN ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 1s var(--transition-slow), visibility 1s var(--transition-slow);
}

.loading-screen::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 30% 70%, rgba(0, 212, 255, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(255, 0, 128, 0.2) 0%, transparent 50%);
    animation: loadingBg 4s ease-in-out infinite alternate;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    position: relative;
    z-index: 1;
}

.loading-logo {
    margin-bottom: var(--space-2xl);
}

.logo-text {
    font-family: var(--font-display);
    font-size: clamp(2rem, 5vw, 4rem);
    font-weight: 700;
    letter-spacing: 0.2em;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--space-sm);
    animation: logoGlow 2s ease-in-out infinite alternate;
}

.logo-subtitle {
    font-family: var(--font-mono);
    font-size: 0.9rem;
    letter-spacing: 0.3em;
    color: var(--text-secondary);
    text-transform: uppercase;
    opacity: 0.8;
}

.loading-progress-container {
    position: relative;
    margin-bottom: var(--space-2xl);
}

.loading-progress {
    width: 300px;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    overflow: hidden;
    position: relative;
    margin: 0 auto var(--space-sm);
}

.loading-progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-sm);
    animation: loadingProgress 3s ease-out forwards;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.loading-percentage {
    font-family: var(--font-mono);
    font-size: 0.9rem;
    color: var(--accent-primary);
    letter-spacing: 0.1em;
}

.loading-neural-network {
    position: relative;
    width: 200px;
    height: 100px;
    margin: 0 auto;
}

.neural-node {
    position: absolute;
    width: 8px;
    height: 8px;
    background: var(--accent-primary);
    border-radius: var(--radius-full);
    box-shadow: 0 0 10px var(--accent-primary);
    animation: neuralPulse 2s ease-in-out infinite;
}

.neural-node:nth-child(1) {
    top: 20px;
    left: 20px;
    animation-delay: 0s;
}

.neural-node:nth-child(2) {
    top: 60px;
    left: 100px;
    animation-delay: 0.5s;
}

.neural-node:nth-child(3) {
    top: 30px;
    right: 20px;
    animation-delay: 1s;
}

.neural-connection {
    position: absolute;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
    animation: neuralFlow 3s ease-in-out infinite;
}

.neural-connection:nth-child(4) {
    top: 24px;
    left: 28px;
    width: 76px;
    transform: rotate(25deg);
    animation-delay: 0.2s;
}

.neural-connection:nth-child(5) {
    top: 45px;
    right: 28px;
    width: 76px;
    transform: rotate(-25deg);
    animation-delay: 0.7s;
}

/* ===== FUTURISTIC NAVIGATION ===== */
.navbar {
    position: fixed;
    top: var(--space-lg);
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - var(--space-2xl));
    max-width: 1200px;
    background: var(--bg-glass);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    z-index: 1000;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-glass);
}

.navbar.hidden {
    transform: translateX(-50%) translateY(-120%);
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-hero);
    border-radius: var(--radius-xl);
    opacity: 0;
    transition: var(--transition-smooth);
    z-index: -1;
}

.navbar:hover::before {
    opacity: 1;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-md) var(--space-xl);
}

.nav-logo {
    display: flex;
    align-items: center;
}

.logo-icon {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    text-decoration: none;
    transition: var(--transition-smooth);
}

.logo-hexagon {
    width: 32px;
    height: 32px;
    background: var(--gradient-primary);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-neon);
}

.logo-icon:hover .logo-hexagon {
    transform: rotate(60deg) scale(1.1);
    box-shadow: var(--shadow-neon-hover);
}

.logo-text {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    letter-spacing: 0.1em;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--space-xl);
}

.nav-link {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    transition: var(--transition-smooth);
    padding: var(--space-sm);
    border-radius: var(--radius-md);
}

.nav-text {
    font-family: var(--font-mono);
    font-size: 0.8rem;
    font-weight: 500;
    color: var(--text-secondary);
    letter-spacing: 0.1em;
    text-transform: uppercase;
    transition: var(--transition-smooth);
}

.nav-number {
    font-family: var(--font-mono);
    font-size: 0.7rem;
    color: var(--text-muted);
    transition: var(--transition-smooth);
}

.nav-link:hover .nav-text,
.nav-link.active .nav-text {
    color: var(--accent-primary);
    text-shadow: 0 0 10px var(--accent-primary);
}

.nav-link:hover .nav-number,
.nav-link.active .nav-number {
    color: var(--accent-primary);
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-glass);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    opacity: 0;
    transition: var(--transition-smooth);
    z-index: -1;
}

.nav-link:hover::before,
.nav-link.active::before {
    opacity: 1;
    border-color: rgba(0, 212, 255, 0.3);
    box-shadow: var(--shadow-neon);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-smooth);
}

.toggle-line {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    border-radius: 2px;
    transition: var(--transition-smooth);
}

.nav-toggle:hover .toggle-line {
    background: var(--accent-primary);
    box-shadow: 0 0 10px var(--accent-primary);
}

/* ===== REVOLUTIONARY HERO SECTION ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding-top: 120px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: var(--z-background);
}

.hero-image-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    filter: brightness(0.3) contrast(1.2) saturate(1.1);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 100%),
        radial-gradient(circle at 30% 70%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(255, 0, 128, 0.1) 0%, transparent 50%);
}

.hero-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.3;
    animation: gridMove 20s linear infinite;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="%2300d4ff" opacity="0.5"><animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="30" r="1" fill="%23ff0080" opacity="0.3"><animate attributeName="opacity" values="0.3;0.8;0.3" dur="4s" repeatCount="indefinite"/></circle><circle cx="60" cy="70" r="1" fill="%2300ff88" opacity="0.4"><animate attributeName="opacity" values="0.4;0.9;0.4" dur="2s" repeatCount="indefinite"/></circle></svg>') repeat;
    background-size: 200px 200px;
    animation: particleFloat 30s linear infinite;
}

.hero-content {
    position: relative;
    z-index: var(--z-content);
    text-align: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
    opacity: 0;
    transform: translateY(50px);
    transition: var(--transition-slow);
}

.hero-main {
    margin-bottom: var(--space-3xl);
}

.hero-badge {
    position: relative;
    display: inline-flex;
    align-items: center;
    padding: var(--space-sm) var(--space-lg);
    background: var(--bg-glass);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-xl);
    backdrop-filter: blur(20px);
    overflow: hidden;
}

.badge-text {
    font-family: var(--font-mono);
    font-size: 0.8rem;
    color: var(--accent-primary);
    letter-spacing: 0.2em;
    text-transform: uppercase;
    font-weight: 600;
}

.badge-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
    animation: badgeGlow 3s ease-in-out infinite;
}

.hero-title {
    margin-bottom: var(--space-xl);
}

.title-line {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
}

.title-word {
    font-family: var(--font-display);
    font-size: clamp(3rem, 12vw, 8rem);
    font-weight: 800;
    color: var(--text-primary);
    letter-spacing: -0.02em;
    position: relative;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.1);
}

.title-accent {
    font-family: var(--font-display);
    font-size: clamp(3rem, 12vw, 8rem);
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 50px rgba(0, 212, 255, 0.5);
    animation: titleGlow 3s ease-in-out infinite alternate;
}

.title-subtitle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-lg);
}

.subtitle-text {
    font-family: var(--font-mono);
    font-size: clamp(0.9rem, 2vw, 1.2rem);
    color: var(--text-secondary);
    letter-spacing: 0.3em;
    text-transform: uppercase;
    font-weight: 500;
}

.subtitle-divider {
    width: 60px;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.hero-description {
    margin-bottom: var(--space-2xl);
}

.description-text {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    color: var(--text-secondary);
    line-height: 1.7;
    max-width: 700px;
    margin: 0 auto;
    font-weight: 300;
}

.hero-cta {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-lg);
    margin-bottom: var(--space-3xl);
}

.cta-primary {
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    background: var(--gradient-primary);
    color: var(--text-primary);
    border: none;
    border-radius: var(--radius-lg);
    font-family: var(--font-mono);
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    cursor: pointer;
    transition: var(--transition-smooth);
    overflow: hidden;
    box-shadow: var(--shadow-neon);
}

.cta-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-neon-hover);
}

.cta-arrow {
    width: 20px;
    height: 20px;
    transition: var(--transition-smooth);
}

.cta-primary:hover .cta-arrow {
    transform: translateX(5px);
}

.cta-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: var(--transition-smooth);
}

.cta-primary:hover .cta-glow {
    left: 100%;
}

.cta-secondary {
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    background: transparent;
    color: var(--text-primary);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    font-family: var(--font-mono);
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    cursor: pointer;
    transition: var(--transition-smooth);
    backdrop-filter: blur(20px);
}

.cta-secondary:hover {
    border-color: var(--accent-primary);
    color: var(--accent-primary);
    box-shadow: var(--shadow-neon);
    transform: translateY(-3px);
}

.cta-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid var(--accent-primary);
    border-radius: var(--radius-lg);
    opacity: 0;
    transition: var(--transition-smooth);
}

.cta-secondary:hover .cta-border {
    opacity: 1;
}

/* ===== KEYFRAME ANIMATIONS ===== */
@keyframes loadingBg {
    0% {
        opacity: 0.3;
    }

    100% {
        opacity: 0.7;
    }
}

@keyframes logoGlow {
    0% {
        filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.3));
    }

    100% {
        filter: drop-shadow(0 0 40px rgba(0, 212, 255, 0.6));
    }
}

@keyframes loadingProgress {
    0% {
        width: 0%;
    }

    100% {
        width: 100%;
    }
}

@keyframes neuralPulse {

    0%,
    100% {
        transform: scale(1);
        opacity: 0.5;
    }

    50% {
        transform: scale(1.5);
        opacity: 1;
    }
}

@keyframes neuralFlow {
    0% {
        opacity: 0;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

@keyframes gridMove {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(50px, 50px);
    }
}

@keyframes particleFloat {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(-200px, -200px);
    }
}

@keyframes badgeGlow {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}

@keyframes titleGlow {
    0% {
        filter: drop-shadow(0 0 30px rgba(0, 212, 255, 0.3));
        transform: scale(1);
    }

    100% {
        filter: drop-shadow(0 0 60px rgba(0, 212, 255, 0.6));
        transform: scale(1.02);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes particleMove {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 0;
    }

    10% {
        opacity: 1;
    }

    90% {
        opacity: 1;
    }

    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--space-md);
    }

    .navbar {
        width: calc(100% - var(--space-lg));
        top: var(--space-md);
    }

    .nav-container {
        padding: var(--space-sm) var(--space-lg);
    }

    .nav-menu {
        gap: var(--space-lg);
    }

    .hero-cta {
        flex-direction: column;
        gap: var(--space-md);
    }

    .cta-primary,
    .cta-secondary {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100px;
        left: var(--space-md);
        right: var(--space-md);
        background: var(--bg-glass);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-xl);
        padding: var(--space-lg);
        flex-direction: column;
        gap: var(--space-md);
        transform: translateY(-20px);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-smooth);
        box-shadow: var(--shadow-glass);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .toggle-line:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active .toggle-line:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .toggle-line:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero {
        padding-top: 140px;
        min-height: 90vh;
    }

    .title-line {
        flex-direction: column;
        gap: var(--space-sm);
    }

    .title-subtitle {
        flex-direction: column;
        gap: var(--space-sm);
    }

    .subtitle-divider {
        width: 40px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--space-sm);
    }

    .navbar {
        width: calc(100% - var(--space-md));
    }

    .nav-container {
        padding: var(--space-sm) var(--space-md);
    }

    .hero-badge {
        padding: var(--space-xs) var(--space-md);
    }

    .badge-text {
        font-size: 0.7rem;
    }

    .description-text {
        font-size: 1rem;
    }

    .cta-primary,
    .cta-secondary {
        padding: var(--space-sm) var(--space-lg);
        font-size: 0.8rem;
    }
}

/* ===== UTILITY CLASSES ===== */
.animate-in {
    animation: fadeInUp 0.8s ease-out forwards;
}

.text-center {
    text-align: center;
}

.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}